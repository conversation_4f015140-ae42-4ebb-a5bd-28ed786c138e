linters-settings:
  gosec:
    excludes:
      - G115
  mnd:
    ignored-numbers:
      - '1'
      - '2'
      - '3'
  funlen:
    lines: -1
    statements: 100
    ignore-comments: true
  gocritic:
    enabled-tags:
      - diagnostic
      - performance
      - style
    disabled-checks:
      - ifElseChain
      - typeAssertChain
      - yodaStyleExpr
    settings:
      hugeParam:
        sizeThreshold: 256
      rangeValCopy:
        sizeThreshold: 256
  gocyclo:
    min-complexity: 50
  cyclop:
    max-complexity: 30
  gofmt:
    rewrite-rules:
      - pattern: 'interface{}'
        replacement: 'any'
      - pattern: 'a[b:len(a)]'
        replacement: 'a[b:]'
  goimports:
    local-prefixes: inv-cloud-platform
  govet:
    enable-all: true
    disable:
      - fieldalignment
    settings:
      shadow:
        strict: true
  #lll:
  #  line-length: 140
  misspell:
    locale: US
  nolintlint:
    allow-unused: false # report any unused nolint directives
    require-explanation: false # don't require an explanation for nolint directives
    require-specific: false # don't require nolint directives to be specific about which linter is being skipped
  revive:
    rules:
      - name: unexported-return
        disabled: true
      - name: unused-parameter

linters:
  disable-all: true
  enable:
    - bodyclose
    - cyclop
    - decorder
    - dogsled
    - errcheck
    - errorlint
    - copyloopvar
    - funlen
    - gci
    - gocheckcompilerdirectives
    - gochecknoinits
    - goconst
    - gocritic
    - gocyclo
    - gofmt
    - mnd
    - goprintffuncname
    - gosec
    - gosimple
    - govet
    - ineffassign
    - perfsprint
    #- lll
    - misspell
    - nakedret
    #- nlreturn
    - noctx
    - nolintlint
    - revive
    - staticcheck
    - stylecheck
    - typecheck
    - unconvert
    - unparam
    - unused
    - whitespace
    - errname

  # don't enable:
  # - asciicheck
  # - gochecknoglobals
  # - gocognit
  # - godot
  # - godox
  # - goerr113
  # - nestif
  # - prealloc
  # - testpackage
  # - wsl

issues:
  max-same-issues: 20

  exclude-rules:
    - path: _test\.go
      linters:
        - gocyclo
        - gosec
        - gocritic
        - stylecheck
run:
  timeout: 10m
output:
  show-stats: true