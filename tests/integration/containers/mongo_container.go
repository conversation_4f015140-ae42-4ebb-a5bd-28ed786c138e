package containers

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	tsuit "github.com/stretchr/testify/suite"
	"github.com/testcontainers/testcontainers-go"
	"github.com/testcontainers/testcontainers-go/wait"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"inv-cloud-platform/tmpl-hexa-rest-api/config"
	"inv-cloud-platform/tmpl-hexa-rest-api/internal/adapter/mongodb"
	portpkg "inv-cloud-platform/tmpl-hexa-rest-api/internal/core/port"
)

type ReceiptRepoIntegrationTestSuite struct {
	tsuit.Suite
	mongoContainer testcontainers.Container
	client         *mongo.Client
	receiptRepo    portpkg.ReceiptService
	config         *config.Configs
}

func (suite *ReceiptRepoIntegrationTestSuite) SetupSuite() {
	ctx := context.Background()

	req := testcontainers.ContainerRequest{
		Image:        "mongo:latest",
		ExposedPorts: []string{"27017/tcp"},
		WaitingFor:   wait.ForListeningPort("27017/tcp"),
	}
	mongoContainer, err := testcontainers.GenericContainer(ctx, testcontainers.GenericContainerRequest{
		ContainerRequest: req,
		Started:          true,
	})
	if err != nil {
		suite.T().Fatal(err)
	}

	suite.mongoContainer = mongoContainer

	host, err := mongoContainer.Host(ctx)
	if err != nil {
		suite.T().Fatal(err)
	}

	port, err := mongoContainer.MappedPort(ctx, "27017")
	if err != nil {
		suite.T().Fatal(err)
	}

	uri := "mongodb://" + host + ":" + port.Port()
	clientOptions := options.Client().ApplyURI(uri)
	client, err := mongo.Connect(ctx, clientOptions)
	if err != nil {
		suite.T().Fatal(err)
	}

	suite.client = client
	suite.config = &config.Configs{
		MongoDB: &config.Mongodb{
			Database:     "local",
			MyCollection: "test",
		},
	}
	suite.receiptRepo = mongodb.NewReceiptRepo(suite.client, suite.config)
}

func (suite *ReceiptRepoIntegrationTestSuite) TearDownSuite() {
	ctx := context.Background()
	if err := suite.client.Disconnect(ctx); err != nil {
		suite.T().Fatal(err)
	}
	if err := suite.mongoContainer.Terminate(ctx); err != nil {
		suite.T().Fatal(err)
	}
}

func (suite *ReceiptRepoIntegrationTestSuite) TestGetReceiptSuccess() {
	ctx := context.Background()
	authRef := "authRef"
	id := "id"
	expectedReceipt := mongodb.Receipt{
		AuthorizationReference: &authRef,
		ID:                     &id,
		Template:               mongodb.Template{HTML: "<html>Receipt</html>"},
		Downloaded:             true,
	}

	collection := suite.client.Database(suite.config.MongoDB.Database).Collection(suite.config.MongoDB.MyCollection)
	_, err := collection.InsertOne(ctx, expectedReceipt)
	assert.NoError(suite.T(), err)

	template, firstVisit, err := suite.receiptRepo.GetReceipt(ctx, authRef, id)

	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), template)
	assert.NotNil(suite.T(), firstVisit)
	assert.Equal(suite.T(), "<html>Receipt</html>", string(template))
	assert.True(suite.T(), *firstVisit)
}

func (suite *ReceiptRepoIntegrationTestSuite) TestGetReceiptError() {
	ctx := context.Background()
	authRef := "authRef"
	id := "id"

	template, firstVisit, err := suite.receiptRepo.GetReceipt(ctx, authRef, id)

	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), template)
	assert.Nil(suite.T(), firstVisit)
}

func TestReceiptRepoIntegrationTestSuite(t *testing.T) {
	tsuit.Run(t, new(ReceiptRepoIntegrationTestSuite))
}
