package config

import (
	"log"
	"time"

	"github.com/caarlos0/env/v11"
)

type Configs struct {
	Auth    *Auth
	App     *App
	MongoDB *Mongodb
	HTTP    *HTTP
}

func New() *Configs {
	cfg := &Configs{
		Auth:    &Auth{},
		App:     &App{},
		MongoDB: &Mongodb{},
		HTTP:    &HTTP{},
	}
	if err := env.Parse(cfg); err != nil {
		log.Fatalf("Failed to load configs: %v", err)
	}

	return cfg
}

type Auth struct {
	KeycloakHost    string `env:"KC_HOST" envDefault:"https://auth.hub-dev.invenco.com"`
	KeycloakRealm   string `env:"KC_REALM" envDefault:"invenco-hub"`
	ClientID        string `env:"AUTH_CLIENT_ID"`
	ClientSecret    string `env:"AUTH_CLIENT_SECRET"`
	ServiceUsername string `env:"AUTH_SERVICE_USERNAME"`
	ServicePassword string `env:"AUTH_SERVICE_PASSWORD"`
}

type Mongodb struct {
	Database     string `env:"MONGO_DATABASE" envDefault:"local"`
	MyCollection string `env:"MONGO_COLLECTION" envDefault:"test"`
}

type HTTP struct {
	Timeout  time.Duration `env:"HTTP_CLIENT_TIMEOUT"   envDefault:"30s"`
	RetryMax int           `env:"HTTP_CLIENT_RETRY_MAX" envDefault:"3"`
}

type App struct {
	LogLevel    string        `env:"LOG_LEVEL" envDefault:"debug"`
	ServerPort  int           `env:"SERVER_PORT" envDefault:"8080"`
	ReadTimeout time.Duration `env:"SERVER_READ_TIMEOUT" envDefault:"500s"`
}
