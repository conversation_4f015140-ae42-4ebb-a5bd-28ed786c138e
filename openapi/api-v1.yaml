openapi: 3.0.2
info:
  version: 1.0.0
  title: Invenco Receipt API
  description: Invenco Receipt API
  contact: {}
tags:
  - name: users
    description: Users CRUD

paths:

  /v1/{auth_ref}/{id}:
    get:
      summary: Retrieve a receipt
      operationId: GetReceipt
      parameters:
        - name: auth_ref
          in: path
          required: true
          schema:
            type: string
          description: Authorization reference
        - name: id
          in: path
          required: true
          schema:
            type: string
          description: Unique identifier
      responses:
        '200':
          description: Receipt retrieved successfully
          content:
            application/pdf:
              schema:
                $ref: "#/components/schemas/RetrieveReceipt"

        "400":
          $ref: "#/components/responses/400"
        "401":
          $ref: "#/components/responses/401"
        "403":
          $ref: "#/components/responses/403"
        "500":
          $ref: "#/components/responses/500"
        "503":
          $ref: "#/components/responses/503"

components:
  # securitySchemes:
  #   bearerAuth:
  #     type: http
  #     scheme: bearer
  #     bearerFormat: JWT
  schemas:
    CreateUserRQ:
      properties:
        name:
          type: string
        email:
          type: string
      required:
        - name
        - email
      type: object
    CreateUserRS:
      type: object
    CreateReceipt:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: "452cf8a1-79aa-4a0e-9aee-dc788586053c"
        authorization_reference:
          type: string
          example: "pp-100100-1948213567"
        receipt:
          type: string
          format: binary
          description: PDF file of the receipt
    RetrieveReceipt:
      type: string
      format: binary
      description: PDF file of the receipt
    Error:
      type: object
      properties:
        code:
          allOf:
            - $ref: '#/components/schemas/ErrorCode'
          x-go-type-skip-optional-pointer: true
        message:
          type: string
          example: Required field is missed
          x-go-type-skip-optional-pointer: true
      required:
        - code
        - message
    ErrorCode:
      enum:
        - NotAuthorized
        - InvalidParameter
        - InternalServerError
      type: string
      x-enum-varnames:
        - ErrorCodeNotAuthorized
        - ErrorCodeInvalidParameter
        - ErrorCodeInternalServerError
  responses:
    200:
      description: Success
    400:
      description: Bad Request
      headers:
        x-hub-request-id:
          description: Unique identifier for the request
          schema:
            type: string
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/Error"
    401:
      description: Authentication failed
    403:
      description: Authorization failed
      headers:
        x-hub-request-id:
          description: Unique identifier for the request
          schema:
            type: string
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
    404:
      description: Not Found
      headers:
        x-hub-request-id:
          description: Unique identifier for the request
          schema:
            type: string
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
    500:
      description: Internal Server Error
      headers:
        x-hub-request-id:
          description: Unique identifier for the request
          schema:
            type: string
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
    503:
      description: Service Unavailable