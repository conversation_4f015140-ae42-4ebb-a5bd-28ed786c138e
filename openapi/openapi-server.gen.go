// Package openapi provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/oapi-codegen/oapi-codegen/v2 version v2.4.1 DO NOT EDIT.
package openapi

import (
	"bytes"
	"compress/gzip"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"path"
	"strings"

	"github.com/getkin/kin-openapi/openapi3"
	"github.com/go-chi/chi/v5"
	"github.com/oapi-codegen/runtime"
	strictnethttp "github.com/oapi-codegen/runtime/strictmiddleware/nethttp"
	openapi_types "github.com/oapi-codegen/runtime/types"
)

// Defines values for ErrorCode.
const (
	ErrorCodeInternalServerError ErrorCode = "InternalServerError"
	ErrorCodeInvalidParameter    ErrorCode = "InvalidParameter"
	ErrorCodeNotAuthorized       ErrorCode = "NotAuthorized"
)

// Error defines model for Error.
type Error struct {
	Code    ErrorCode `json:"code"`
	Message string    `json:"message"`
}

// ErrorCode defines model for ErrorCode.
type ErrorCode string

// RetrieveReceipt PDF file of the receipt
type RetrieveReceipt = openapi_types.File

// N400 defines model for 400.
type N400 = Error

// N403 defines model for 403.
type N403 = Error

// N500 defines model for 500.
type N500 = Error

// ServerInterface represents all server handlers.
type ServerInterface interface {
	// Retrieve a receipt
	// (GET /v1/{auth_ref}/{id})
	GetReceipt(w http.ResponseWriter, r *http.Request, authRef string, id string)
}

// Unimplemented server implementation that returns http.StatusNotImplemented for each endpoint.

type Unimplemented struct{}

// Retrieve a receipt
// (GET /v1/{auth_ref}/{id})
func (_ Unimplemented) GetReceipt(w http.ResponseWriter, r *http.Request, authRef string, id string) {
	w.WriteHeader(http.StatusNotImplemented)
}

// ServerInterfaceWrapper converts contexts to parameters.
type ServerInterfaceWrapper struct {
	Handler            ServerInterface
	HandlerMiddlewares []MiddlewareFunc
	ErrorHandlerFunc   func(w http.ResponseWriter, r *http.Request, err error)
}

type MiddlewareFunc func(http.Handler) http.Handler

// GetReceipt operation middleware
func (siw *ServerInterfaceWrapper) GetReceipt(w http.ResponseWriter, r *http.Request) {

	var err error

	// ------------- Path parameter "auth_ref" -------------
	var authRef string

	err = runtime.BindStyledParameterWithOptions("simple", "auth_ref", chi.URLParam(r, "auth_ref"), &authRef, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "auth_ref", Err: err})
		return
	}

	// ------------- Path parameter "id" -------------
	var id string

	err = runtime.BindStyledParameterWithOptions("simple", "id", chi.URLParam(r, "id"), &id, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "id", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetReceipt(w, r, authRef, id)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r)
}

type UnescapedCookieParamError struct {
	ParamName string
	Err       error
}

func (e *UnescapedCookieParamError) Error() string {
	return fmt.Sprintf("error unescaping cookie parameter '%s'", e.ParamName)
}

func (e *UnescapedCookieParamError) Unwrap() error {
	return e.Err
}

type UnmarshalingParamError struct {
	ParamName string
	Err       error
}

func (e *UnmarshalingParamError) Error() string {
	return fmt.Sprintf("Error unmarshaling parameter %s as JSON: %s", e.ParamName, e.Err.Error())
}

func (e *UnmarshalingParamError) Unwrap() error {
	return e.Err
}

type RequiredParamError struct {
	ParamName string
}

func (e *RequiredParamError) Error() string {
	return fmt.Sprintf("Query argument %s is required, but not found", e.ParamName)
}

type RequiredHeaderError struct {
	ParamName string
	Err       error
}

func (e *RequiredHeaderError) Error() string {
	return fmt.Sprintf("Header parameter %s is required, but not found", e.ParamName)
}

func (e *RequiredHeaderError) Unwrap() error {
	return e.Err
}

type InvalidParamFormatError struct {
	ParamName string
	Err       error
}

func (e *InvalidParamFormatError) Error() string {
	return fmt.Sprintf("Invalid format for parameter %s: %s", e.ParamName, e.Err.Error())
}

func (e *InvalidParamFormatError) Unwrap() error {
	return e.Err
}

type TooManyValuesForParamError struct {
	ParamName string
	Count     int
}

func (e *TooManyValuesForParamError) Error() string {
	return fmt.Sprintf("Expected one value for %s, got %d", e.ParamName, e.Count)
}

// Handler creates http.Handler with routing matching OpenAPI spec.
func Handler(si ServerInterface) http.Handler {
	return HandlerWithOptions(si, ChiServerOptions{})
}

type ChiServerOptions struct {
	BaseURL          string
	BaseRouter       chi.Router
	Middlewares      []MiddlewareFunc
	ErrorHandlerFunc func(w http.ResponseWriter, r *http.Request, err error)
}

// HandlerFromMux creates http.Handler with routing matching OpenAPI spec based on the provided mux.
func HandlerFromMux(si ServerInterface, r chi.Router) http.Handler {
	return HandlerWithOptions(si, ChiServerOptions{
		BaseRouter: r,
	})
}

func HandlerFromMuxWithBaseURL(si ServerInterface, r chi.Router, baseURL string) http.Handler {
	return HandlerWithOptions(si, ChiServerOptions{
		BaseURL:    baseURL,
		BaseRouter: r,
	})
}

// HandlerWithOptions creates http.Handler with additional options
func HandlerWithOptions(si ServerInterface, options ChiServerOptions) http.Handler {
	r := options.BaseRouter

	if r == nil {
		r = chi.NewRouter()
	}
	if options.ErrorHandlerFunc == nil {
		options.ErrorHandlerFunc = func(w http.ResponseWriter, r *http.Request, err error) {
			http.Error(w, err.Error(), http.StatusBadRequest)
		}
	}
	wrapper := ServerInterfaceWrapper{
		Handler:            si,
		HandlerMiddlewares: options.Middlewares,
		ErrorHandlerFunc:   options.ErrorHandlerFunc,
	}

	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/v1/{auth_ref}/{id}", wrapper.GetReceipt)
	})

	return r
}

type N400ResponseHeaders struct {
	XHubRequestId string
}
type N400JSONResponse struct {
	Body Error

	Headers N400ResponseHeaders
}

type N401Response struct {
}

type N403ResponseHeaders struct {
	XHubRequestId string
}
type N403JSONResponse struct {
	Body Error

	Headers N403ResponseHeaders
}

type N500ResponseHeaders struct {
	XHubRequestId string
}
type N500JSONResponse struct {
	Body Error

	Headers N500ResponseHeaders
}

type N503Response struct {
}

type GetReceiptRequestObject struct {
	AuthRef string `json:"auth_ref"`
	Id      string `json:"id"`
}

type GetReceiptResponseObject interface {
	VisitGetReceiptResponse(w http.ResponseWriter) error
}

type GetReceipt200ApplicationpdfResponse struct {
	Body          io.Reader
	ContentLength int64
}

func (response GetReceipt200ApplicationpdfResponse) VisitGetReceiptResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/pdf")
	if response.ContentLength != 0 {
		w.Header().Set("Content-Length", fmt.Sprint(response.ContentLength))
	}
	w.WriteHeader(200)

	if closer, ok := response.Body.(io.ReadCloser); ok {
		defer closer.Close()
	}
	_, err := io.Copy(w, response.Body)
	return err
}

type GetReceipt400JSONResponse struct{ N400JSONResponse }

func (response GetReceipt400JSONResponse) VisitGetReceiptResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.Header().Set("x-hub-request-id", fmt.Sprint(response.Headers.XHubRequestId))
	w.WriteHeader(400)

	return json.NewEncoder(w).Encode(response.Body)
}

type GetReceipt401Response = N401Response

func (response GetReceipt401Response) VisitGetReceiptResponse(w http.ResponseWriter) error {
	w.WriteHeader(401)
	return nil
}

type GetReceipt403JSONResponse struct{ N403JSONResponse }

func (response GetReceipt403JSONResponse) VisitGetReceiptResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.Header().Set("x-hub-request-id", fmt.Sprint(response.Headers.XHubRequestId))
	w.WriteHeader(403)

	return json.NewEncoder(w).Encode(response.Body)
}

type GetReceipt500JSONResponse struct{ N500JSONResponse }

func (response GetReceipt500JSONResponse) VisitGetReceiptResponse(w http.ResponseWriter) error {
	w.Header().Set("Content-Type", "application/json")
	w.Header().Set("x-hub-request-id", fmt.Sprint(response.Headers.XHubRequestId))
	w.WriteHeader(500)

	return json.NewEncoder(w).Encode(response.Body)
}

type GetReceipt503Response = N503Response

func (response GetReceipt503Response) VisitGetReceiptResponse(w http.ResponseWriter) error {
	w.WriteHeader(503)
	return nil
}

// StrictServerInterface represents all server handlers.
type StrictServerInterface interface {
	// Retrieve a receipt
	// (GET /v1/{auth_ref}/{id})
	GetReceipt(ctx context.Context, request GetReceiptRequestObject) (GetReceiptResponseObject, error)
}

type StrictHandlerFunc = strictnethttp.StrictHTTPHandlerFunc
type StrictMiddlewareFunc = strictnethttp.StrictHTTPMiddlewareFunc

type StrictHTTPServerOptions struct {
	RequestErrorHandlerFunc  func(w http.ResponseWriter, r *http.Request, err error)
	ResponseErrorHandlerFunc func(w http.ResponseWriter, r *http.Request, err error)
}

func NewStrictHandler(ssi StrictServerInterface, middlewares []StrictMiddlewareFunc) ServerInterface {
	return &strictHandler{ssi: ssi, middlewares: middlewares, options: StrictHTTPServerOptions{
		RequestErrorHandlerFunc: func(w http.ResponseWriter, r *http.Request, err error) {
			http.Error(w, err.Error(), http.StatusBadRequest)
		},
		ResponseErrorHandlerFunc: func(w http.ResponseWriter, r *http.Request, err error) {
			http.Error(w, err.Error(), http.StatusInternalServerError)
		},
	}}
}

func NewStrictHandlerWithOptions(ssi StrictServerInterface, middlewares []StrictMiddlewareFunc, options StrictHTTPServerOptions) ServerInterface {
	return &strictHandler{ssi: ssi, middlewares: middlewares, options: options}
}

type strictHandler struct {
	ssi         StrictServerInterface
	middlewares []StrictMiddlewareFunc
	options     StrictHTTPServerOptions
}

// GetReceipt operation middleware
func (sh *strictHandler) GetReceipt(w http.ResponseWriter, r *http.Request, authRef string, id string) {
	var request GetReceiptRequestObject

	request.AuthRef = authRef
	request.Id = id

	handler := func(ctx context.Context, w http.ResponseWriter, r *http.Request, request interface{}) (interface{}, error) {
		return sh.ssi.GetReceipt(ctx, request.(GetReceiptRequestObject))
	}
	for _, middleware := range sh.middlewares {
		handler = middleware(handler, "GetReceipt")
	}

	response, err := handler(r.Context(), w, r, request)

	if err != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, err)
	} else if validResponse, ok := response.(GetReceiptResponseObject); ok {
		if err := validResponse.VisitGetReceiptResponse(w); err != nil {
			sh.options.ResponseErrorHandlerFunc(w, r, err)
		}
	} else if response != nil {
		sh.options.ResponseErrorHandlerFunc(w, r, fmt.Errorf("unexpected response type: %T", response))
	}
}

// Base64 encoded, gzipped, json marshaled Swagger object
var swaggerSpec = []string{

	"H4sIAAAAAAAC/8RVQXPyNhD9K5ptjzaGj+TiW5q0HS5thg6nDNMR9hqU2pKykj1JGf/3zspgh+ASemi+",
	"E0Zavfd29Xa1h8xU1mjU3kG6B0JnjXYY/txMp/yTGe1Re/6U1pYqk14ZnTw7o3nNZTusJH/9SFhACj8k",
	"A2bS7brkZyJD0LZtBDm6jJRlEEjhJ5mLJb7U6DxEsEOZIwX213hXb2LqtmKV89rp0ZVWLzUKlaP2qlBI",
	"ojAk/A4F9YCDOv9mEVJwnpTespI2gpvp7Bz2rvY7RuzyFIVUJeYQouf/fz2Y3ZD6+4T8ywtz+xVXv9Ae",
	"SctS/IHUIIku8DvkOj+HZUkqQ7HSspGqlJsSgYMPSfGBTm66B0vGInnVdU1mcgwFK8vfC0ifrijOPR9p",
	"1xG8xlsTs8bY/aVsbIIaWcbWKK4VpJ5qbCOo0Dm5DTT4KitbclLcRYowF4XCMhfKiUo5F9xzmva1PG0E",
	"dMCE9KlLbOBe97Bm84yZ5w4ZsmFluq743G/GH00dxCx0I0uVP0qSFTJb1DuhM0JX2PWYbMaMG0laVlzt",
	"p4HxI0u/MUL3bm+Et41giZ4UNrjEDJX15/Z4fPhFFKpEYYqD17rACApDlfSQwkZpSW9nxQ+WU7owx/6S",
	"GeOPNEeDOjPiIEHcPS4YS/lw1+O7DZLrTs8m08mUb8RY1NIqSGE+mU6+QQRW+l0watLMkr2s/e5PwqJN",
	"9ipveXmLIV+2dOj2RQ4p/Ip+2adoj6V0wd6X5hdhgYQ6Y+Mo3mZ2iIDvD1I4ssN7p7H3LvVs9OkIGCdT",
	"+X+iWUenr+G3iyPR5sX1E/GjvUZm4/Fi6RCaC1dnGTpX1GX51r1G03/j6XUnHDS8c5/Fzt69cp/FzmF4",
	"Ji7H3nYabq/B5aAwZuuq4u5J+1YUsm+yUC4vt2P2WzkkJ+6Xq4fh4mteg3bd/hMAAP//UacbPu4IAAA=",
}

// GetSwagger returns the content of the embedded swagger specification file
// or error if failed to decode
func decodeSpec() ([]byte, error) {
	zipped, err := base64.StdEncoding.DecodeString(strings.Join(swaggerSpec, ""))
	if err != nil {
		return nil, fmt.Errorf("error base64 decoding spec: %w", err)
	}
	zr, err := gzip.NewReader(bytes.NewReader(zipped))
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}
	var buf bytes.Buffer
	_, err = buf.ReadFrom(zr)
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}

	return buf.Bytes(), nil
}

var rawSpec = decodeSpecCached()

// a naive cached of a decoded swagger spec
func decodeSpecCached() func() ([]byte, error) {
	data, err := decodeSpec()
	return func() ([]byte, error) {
		return data, err
	}
}

// Constructs a synthetic filesystem for resolving external references when loading openapi specifications.
func PathToRawSpec(pathToFile string) map[string]func() ([]byte, error) {
	res := make(map[string]func() ([]byte, error))
	if len(pathToFile) > 0 {
		res[pathToFile] = rawSpec
	}

	return res
}

// GetSwagger returns the Swagger specification corresponding to the generated code
// in this file. The external references of Swagger specification are resolved.
// The logic of resolving external references is tightly connected to "import-mapping" feature.
// Externally referenced files must be embedded in the corresponding golang packages.
// Urls can be supported but this task was out of the scope.
func GetSwagger() (swagger *openapi3.T, err error) {
	resolvePath := PathToRawSpec("")

	loader := openapi3.NewLoader()
	loader.IsExternalRefsAllowed = true
	loader.ReadFromURIFunc = func(loader *openapi3.Loader, url *url.URL) ([]byte, error) {
		pathToFile := url.String()
		pathToFile = path.Clean(pathToFile)
		getSpec, ok := resolvePath[pathToFile]
		if !ok {
			err1 := fmt.Errorf("path not found: %s", pathToFile)
			return nil, err1
		}
		return getSpec()
	}
	var specData []byte
	specData, err = rawSpec()
	if err != nil {
		return
	}
	swagger, err = loader.LoadFromData(specData)
	if err != nil {
		return
	}
	return
}
