#  Hexagonal Architecture REST API Template

This project serves as a base for Hexagonal Architecture REST API in Vandura. Use it as a template from here and start your new microservice.

## Architecture Notes
The application is build based on the idea of [Hexagonal Architecture](https://en.wikipedia.org/wiki/Hexagonal_architecture_(software)]) approach.
The Hexagonal Architecture, also referred to as Ports and Adapters, is an architectural pattern that allows input by users or external systems to arrive into the Application at a Port via an Adapter.
It also allows output to be sent out from the Application through a Port to an Adapter.
This creates an abstraction layer that protects the core of an application and isolates it from external tools and technologies.

The architecture encourages separation of concerns by organizing code into layers that are loosely coupled and independently replaceable.
The project layout os based on the one described here: [layout](https://github.com/golang-standards/project-layout)

## What is in the template
- This template is using Go >= v1.23
- We are using `zerolog` in this template for logging, the configuration is made in the `internal/comomn/logger` package
- The server is already setup with default middlewares
- A predefined Makefile with useful commands

## First steps
-  Make sure to run `go mod tidy` to ensure all your imports are satisfied
- Run `go get -u ./...` to update to the most recent packages
- Notice that in the template we have `TODO` comments in multiple files, search for them, and make the necessary changes to match your app criteria

## Purpose
TODO: Describe the API purpose here

### Tooling
* golang v1.23
* docker 
* `make` - this project uses Makefiles

### Setup
To set up golang dependencies: `go mod download` and then `go mod tidy`

### Static Analysis Tools
The project uses [golangci-lin](https://golangci-lint.run/usage/quick-start/) lint tool to detect potential issues, bugs, and code style violations in Go codebases.

`make lint`

### Scripts
Many convenience commands are defined in the `Makefile`, run `make help` to learn more.

### Unit Tests
`make test`

### Docker Image
To create and push docker image to the image repo, one needs to create and push tag following the semantic versioning `release/v.a.b.c`
```
  git tag release/v0.0.1 && git tag push origin release/v0.0.1
```

### OpenAPI Generator docs
The app uses OpenAPI Generator to generate API docs. To generate docs, run:
`make codegen`

### Deployment
To deploy the app, one needs to use https://github.com/inv-cloud-platform/{subsystem}-deploy by following the steps listed in README.md.

## API

You can access and interact with this API by accessing one of the available endpoints:

- [`/swagger`](http://localhost:8080/swagger)
- [`/scalar`](http://localhost:8080/scalar)
- [`/redoc`](http://localhost:8080/redoc)