package main

import (
	"context"
	"os"
	"os/signal"
	"syscall"

	"github.com/inv-cloud-platform/hub-com-tools-go/hubmiddlewares"
	"github.com/inv-cloud-platform/hub-com-tools-go/hubmongo"
	"github.com/rs/zerolog/log"
	"inv-cloud-platform/tmpl-hexa-rest-api/config"
	"inv-cloud-platform/tmpl-hexa-rest-api/internal/adapter/mongodb"
	"inv-cloud-platform/tmpl-hexa-rest-api/internal/adapter/resthttp"
	"inv-cloud-platform/tmpl-hexa-rest-api/internal/common"
	"inv-cloud-platform/tmpl-hexa-rest-api/internal/core/usecase"
	"inv-cloud-platform/tmpl-hexa-rest-api/openapi"
)

func main() {
	log.Info().Msg("Application starting ...")
	configs := config.New()
	common.NewLogger(configs.App.LogLevel)

	appCtx := context.Background()

	// https://github.com/inv-cloud-platform/hub-com-tools-go/blob/main/hubmongo/README.md#connect
	mongoClient, errMongo := hubmongo.Connect(appCtx)
	if errMongo != nil {
		log.Fatal().Err(errMongo).Msg("unable to connect to mongodb")
	}

	receiptRepo := mongodb.NewReceiptRepo(mongoClient, configs)
	rcptSvc := usecase.NewCreateReceipt(receiptRepo)
	receiptController := resthttp.NewReceiptController(rcptSvc)
	receiptHandler := openapi.NewStrictHandler(receiptController, nil)

	oapiSpec, errSw := openapi.GetSwagger()
	if errSw != nil {
		log.Warn().Err(errSw).Msg("unable to fetch openapi spec")
	}

	httpServer := resthttp.NewHTTPServer(
		configs.App,
		openapi.Handler(receiptHandler),
		resthttp.MetricsCollector,
		hubmiddlewares.RequestId,
		hubmiddlewares.Spec(
			hubmiddlewares.SpecOptionsTitle(oapiSpec.Info.Title),
			hubmiddlewares.SpecOptionsObjectSpec(oapiSpec),
		),
		// auth.Middleware(&hubauth.MiddlewareConfig{Strategy: hubauth.AllOf, Domains: []string{common.Domain}}),
	)
	go httpServer.Start()
	defer func(ctx context.Context) {
		errS := httpServer.Shutdown(ctx)
		if errS != nil {
			log.Err(errS).Msg("HTTP server shutdown error")
		}
	}(appCtx)

	ctx, stop := signal.NotifyContext(appCtx, os.Interrupt, os.Kill, syscall.SIGTERM, syscall.SIGINT)
	defer stop()
	log.Info().Msg("Waiting for app exiting conditions")

	<-ctx.Done()
	log.Info().Msg("Application stopping ...")
}
