package mongodb

import (
	"context"

	"github.com/rs/zerolog/log"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"inv-cloud-platform/tmpl-hexa-rest-api/config"
)

type receiptRepo struct {
	db   *mongo.Client
	conf *config.Configs
}

func NewReceiptRepo(db *mongo.Client, conf *config.Configs) *receiptRepo {
	return &receiptRepo{
		db:   db,
		conf: conf,
	}
}

type Receipt struct {
	AuthorizationReference *string `bson:"authorization_reference"`
	ID                     *string `bson:"id"`
	// Receipt PDF file of the receipt
	Template   Template `bson:"template"`
	Downloaded bool     `bson:"downloaded"`
}

type Template struct {
	HTML string `bson:"html"`
}

func (s *receiptRepo) GetReceipt(ctx context.Context, authRef, id string) (template []byte, firstVisit *bool, err error) {
	var result Receipt
	filter := bson.M{"id": id, "authorization_reference": authRef}
	err = s.db.Database(s.conf.MongoDB.Database).Collection(s.conf.MongoDB.MyCollection).FindOne(ctx, filter).Decode(&result)
	if err != nil {
		return nil, nil, err
	}

	rcpBytes := []byte(result.Template.HTML)

	if result.Downloaded {
		err = s.UpdateFirstVisit(ctx, false, authRef, id)
		if err != nil {
			log.Warn().Err(err).Msg("First Visit is not updated in DB.")
		}
	}

	return rcpBytes, &result.Downloaded, nil
}

func (s *receiptRepo) UpdateFirstVisit(ctx context.Context, firstVisit bool, authRef, id string) (err error) {
	filter := bson.M{"id": id, "authorization_reference": authRef}

	update := bson.M{"$set": bson.M{"downloaded": firstVisit}}

	result, err := s.db.Database(s.conf.MongoDB.Database).Collection(s.conf.MongoDB.MyCollection).UpdateOne(ctx, filter, update)
	if err != nil {
		return err
	}

	log.Info().Msgf("Matched %v documents and modified %v documents.\n", result.MatchedCount, result.ModifiedCount)
	return err
}
