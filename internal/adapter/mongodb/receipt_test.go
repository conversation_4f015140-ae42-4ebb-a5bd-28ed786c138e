package mongodb

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/suite"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"inv-cloud-platform/tmpl-hexa-rest-api/config"
)

type MongoCollectionMock struct {
	mock.Mock
}

func (m *MongoCollectionMock) FindOne(ctx context.Context, filter any, opts ...*options.FindOneOptions) *mongo.SingleResult {
	args := m.Called(ctx, filter, opts)
	return args.Get(0).(*mongo.SingleResult)
}

func (m *MongoCollectionMock) UpdateOne(ctx context.Context, filter any, update any, opts ...*options.UpdateOptions) (*mongo.UpdateResult, error) {
	args := m.Called(ctx, filter, update, opts)
	return args.Get(0).(*mongo.UpdateResult), args.Error(1)
}

type SingleResultMock struct {
	mock.Mock
}

func (s *SingleResultMock) Decode(v any) error {
	args := s.Called(v)
	return args.Error(0)
}

type ReceiptRepoTestSuite struct {
	suite.Suite
	client         *mongo.Client
	collectionMock *MongoCollectionMock
	receiptRepo    *receiptRepo
	config         *config.Configs
}

func (suite *ReceiptRepoTestSuite) SetupTest() {
	suite.collectionMock = new(MongoCollectionMock)
	suite.config = &config.Configs{
		MongoDB: &config.Mongodb{
			Database:     "local",
			MyCollection: "test",
		},
	}

	clientOptions := options.Client().ApplyURI("mongodb://localhost:27017")
	client, err := mongo.Connect(context.Background(), clientOptions)
	if err != nil {
		suite.T().Fatal(err)
	}

	suite.client = client
	suite.receiptRepo = NewReceiptRepo(suite.client, suite.config)
}

func (suite *ReceiptRepoTestSuite) TestGetReceipt_Error() {
	ctx := context.Background()
	authRef := "54"
	id := "2152"
	singleResultMock := &mongo.SingleResult{}
	suite.collectionMock.On("FindOne", ctx, bson.M{"id": id, "authorization_reference": authRef}).Return(singleResultMock)

	err := singleResultMock.Decode(mock.Anything)
	if err != nil {
		suite.Error(err)
	}

	template, firstVisit, err := suite.receiptRepo.GetReceipt(ctx, authRef, id)

	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), template)
	assert.Nil(suite.T(), firstVisit)
}

func (suite *ReceiptRepoTestSuite) TestUpdateFirstVisit_Success() {
	ctx := context.Background()
	authRef := "authRef"
	id := "id"
	updateResult := &mongo.UpdateResult{MatchedCount: 1, ModifiedCount: 1}
	suite.collectionMock.On("UpdateOne", ctx, bson.M{"id": id, "authorization_reference": authRef}, bson.M{"$set": bson.M{"first_visit": false}}).Return(updateResult, nil)

	err := suite.receiptRepo.UpdateFirstVisit(ctx, false, authRef, id)

	assert.Error(suite.T(), err)
}

func (suite *ReceiptRepoTestSuite) TestUpdateFirstVisit_Error() {
	ctx := context.Background()
	authRef := "authRef"
	id := "id"
	suite.collectionMock.On("UpdateOne", ctx, bson.M{"id": id, "authorization_reference": authRef}, bson.M{"$set": bson.M{"first_visit": false}}).Return(nil, errors.New("unexpected error"))

	err := suite.receiptRepo.UpdateFirstVisit(ctx, false, authRef, id)

	assert.Error(suite.T(), err)
}

func TestReceiptRepoTestSuite(t *testing.T) {
	suite.Run(t, new(ReceiptRepoTestSuite))
}
