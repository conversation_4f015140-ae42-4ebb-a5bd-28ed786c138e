package resthttp

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/suite"
	"inv-cloud-platform/tmpl-hexa-rest-api/internal/core/usecase"
	"inv-cloud-platform/tmpl-hexa-rest-api/openapi"
)

type ReceiptServiceMock struct {
	mock.Mock
}

func (m *ReceiptServiceMock) GetReceipt(ctx context.Context, authRef, id string) ([]byte, *bool, error) {
	args := m.Called(ctx, authRef, id)
	return args.Get(0).([]byte), args.Get(1).(*bool), args.Error(2)
}

type ReceiptControllerTestSuite struct {
	suite.Suite
	receiptSvcMock *ReceiptServiceMock
	controller     *ReceiptController
}

func (suite *ReceiptControllerTestSuite) SetupTest() {
	suite.receiptSvcMock = new(ReceiptServiceMock)
	receiptService := usecase.NewCreateReceipt(suite.receiptSvcMock)
	suite.controller = NewReceiptController(receiptService)
}

func (suite *ReceiptControllerTestSuite) TestGetReceipt_Success() {
	ctx := context.Background()
	req := openapi.GetReceiptRequestObject{
		AuthRef: "11",
		Id:      "1",
	}
	template := []byte("<html>Receipt</html>")
	isFirstVisit := true

	suite.receiptSvcMock.On("GetReceipt", ctx, req.AuthRef, req.Id).Return(template, &isFirstVisit, nil)

	resp, err := suite.controller.GetReceipt(ctx, req)

	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), resp)
	assert.IsType(suite.T(), openapi.GetReceipt200ApplicationpdfResponse{}, resp)
}

func (suite *ReceiptControllerTestSuite) TestGetReceipt_Error() {
	ctx := context.Background()
	req := openapi.GetReceiptRequestObject{
		AuthRef: "15",
		Id:      "87",
	}
	isFirstVisit := true

	suite.receiptSvcMock.On("GetReceipt", ctx, req.AuthRef, req.Id).Return([]uint8{}, &isFirstVisit, errors.New("unexpected error"))

	resp, err := suite.controller.GetReceipt(ctx, req)

	assert.Error(suite.T(), err)
	assert.NotNil(suite.T(), resp)
	assert.IsType(suite.T(), openapi.GetReceipt500JSONResponse{}, resp)
}

func TestReceiptControllerTestSuite(t *testing.T) {
	suite.Run(t, new(ReceiptControllerTestSuite))
}
