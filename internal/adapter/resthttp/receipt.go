package resthttp

import (
	"bytes"
	"context"

	"github.com/inv-cloud-platform/hub-com-tools-go/hubmiddlewares"
	"inv-cloud-platform/tmpl-hexa-rest-api/internal/common"
	"inv-cloud-platform/tmpl-hexa-rest-api/internal/core/usecase"
	"inv-cloud-platform/tmpl-hexa-rest-api/openapi"
)

// Check if operation satisfies interface.
var _ openapi.StrictServerInterface = (*ReceiptController)(nil)

type ReceiptController struct {
	receiptSvc *usecase.ReceiptService
}

func NewReceiptController(rcptSvc *usecase.ReceiptService) *ReceiptController {
	return &ReceiptController{receiptSvc: rcptSvc}
}

func (h *ReceiptController) GetReceipt(ctx context.Context, req openapi.GetReceiptRequestObject) (openapi.GetReceiptResponseObject, error) {
	requestID := hubmiddlewares.GetRequestId(ctx)

	authRef := req.AuthRef
	id := req.Id

	template, isFirstVisit, err := h.receiptSvc.GetReceipt(ctx, authRef, id)
	if err != nil {
		return openapi.GetReceipt500JSONResponse{
			N500JSONResponse: openapi.N500JSONResponse{
				Body: openapi.Error{
					Code:    openapi.ErrorCodeInternalServerError,
					Message: "unexpected error occurred",
				},
				Headers: openapi.N500ResponseHeaders{
					XHubRequestId: requestID,
				},
			},
		}, err
	}

	pdfs := common.GenPDF(string(template), isFirstVisit)

	r := bytes.NewReader(pdfs)

	return openapi.GetReceipt200ApplicationpdfResponse{
		Body: r,
	}, nil
}
