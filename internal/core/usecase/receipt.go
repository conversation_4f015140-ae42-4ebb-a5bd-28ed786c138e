package usecase

import (
	"context"

	"inv-cloud-platform/tmpl-hexa-rest-api/internal/core/port"
)

type ReceiptService struct {
	receiptRepo port.ReceiptService
}

func NewCreateReceipt(receiptRepo port.ReceiptService) *ReceiptService {
	return &ReceiptService{receiptRepo: receiptRepo}
}

func (s *ReceiptService) GetReceipt(ctx context.Context, authRef, id string) (template []byte, isFirstVisit *bool, err error) {
	rcpt, firstVisit, err := s.receiptRepo.GetReceipt(ctx, authRef, id)
	if err != nil {
		return nil, nil, err
	}

	return rcpt, firstVisit, nil
}
