package usecase

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/suite"
)

type ReceiptRepoMock struct {
	mock.Mock
}

func (m *ReceiptRepoMock) GetReceipt(ctx context.Context, authRef, id string) ([]byte, *bool, error) {
	args := m.Called(ctx, authRef, id)
	return args.Get(0).([]byte), args.Get(1).(*bool), args.Error(2)
}

type ReceiptServiceTestSuite struct {
	suite.Suite
	receiptRepoMock *ReceiptRepoMock
	receiptService  *ReceiptService
}

func (suite *ReceiptServiceTestSuite) SetupTest() {
	suite.receiptRepoMock = new(ReceiptRepoMock)
	suite.receiptService = NewCreateReceipt(suite.receiptRepoMock)
}

func (suite *ReceiptServiceTestSuite) TestGetReceipt_Success() {
	ctx := context.Background()
	authRef := "authRef"
	id := "id"
	template := []byte("<html>Receipt</html>")
	isFirstVisit := true

	suite.receiptRepoMock.On("GetReceipt", ctx, authRef, id).Return(template, &isFirstVisit, nil)

	result, firstVisit, err := suite.receiptService.GetReceipt(ctx, authRef, id)

	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), result)
	assert.NotNil(suite.T(), firstVisit)
	assert.Equal(suite.T(), "<html>Receipt</html>", string(result))
	assert.True(suite.T(), *firstVisit)
}

func (suite *ReceiptServiceTestSuite) TestGetReceipt_Error() {
	ctx := context.Background()
	authRef := "authRef"
	id := "id"
	isFirstVisit := true

	suite.receiptRepoMock.On("GetReceipt", ctx, authRef, id).Return([]uint8{}, &isFirstVisit, errors.New("unexpected error"))

	result, firstVisit, err := suite.receiptService.GetReceipt(ctx, authRef, id)

	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), result)
	assert.Nil(suite.T(), firstVisit)
}

func TestReceiptServiceTestSuite(t *testing.T) {
	suite.Run(t, new(ReceiptServiceTestSuite))
}
